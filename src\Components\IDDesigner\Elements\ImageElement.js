import React, { forwardRef, useState, useRef } from 'react';
import { Rect, Text, Group, Image } from 'react-konva';
import { useDispatch } from 'react-redux';
import { updateElement } from '../../../redux/idDesignerSlice';

const ImageElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const dispatch = useDispatch();
  const [image, setImage] = useState(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const hasImage = element.src && element.src.trim() !== '';

  // Load image when src changes
  React.useEffect(() => {
    if (element.src) {
      const img = new window.Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        setImage(img);
      };
      img.src = element.src;
    }
  }, [element.src]);

  const handleClick = (e) => {
    // Only allow upload for static images (not dynamic)
    if (!hasImage && !isPreviewMode && !element.isDynamic) {
      // Open file dialog
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = element.acceptedTypes || 'image/*';
      input.onchange = (event) => {
        const file = event.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            dispatch(updateElement({
              id: element.id,
              properties: { src: e.target.result }
            }));
          };
          reader.readAsDataURL(file);
        }
      };
      input.click();
    } else {
      onClick(e);
    }
  };

  const getPlaceholderText = () => {
    if (element.isDynamic) {
      if (element.imageType === 'photo') {
        return '👤\nPhoto\n(Dynamic)';
      } else if (element.imageType === 'logo') {
        return '🏢\nLogo\n(Dynamic)';
      }
      return '🖼️\nDynamic Image\n(Template)';
    } else {
      if (element.imageType === 'photo') {
        return '👤\nPhoto\nClick to upload';
      } else if (element.imageType === 'logo') {
        return '🏢\nLogo\nClick to upload';
      }
      return '🖼️\nStatic Image\nClick to upload';
    }
  };

  const getPlaceholderColor = () => {
    if (element.isDynamic) {
      // Dynamic images have a different color scheme
      if (element.imageType === 'photo') {
        return 'rgba(79, 38, 131, 0.08)'; // Theme primary with transparency for dynamic photo
      } else if (element.imageType === 'logo') {
        return 'rgba(79, 38, 131, 0.06)'; // Theme primary with transparency for dynamic logo
      }
      return 'rgba(79, 38, 131, 0.04)'; // Very light theme color for dynamic images
    } else {
      // Static images
      if (element.imageType === 'photo') {
        return 'rgba(79, 38, 131, 0.1)'; // Theme primary with transparency for static photo
      } else if (element.imageType === 'logo') {
        return 'rgba(79, 38, 131, 0.12)'; // Theme primary with transparency for static logo
      }
      return '#f5f5f5'; // Gray for static images
    }
  };

  return (
    <Group
      ref={ref}
      x={element.x}
      y={element.y}
      offsetX={0}
      offsetY={0}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={handleClick}
      onDragEnd={onDragEnd}
    >
      {hasImage && image ? (
        <Image
          x={0}
          y={0}
          width={element.width}
          height={element.height}
          image={image}
        />
      ) : (
        <>
          {/* Placeholder rectangle */}
          <Rect
            x={0}
            y={0}
            width={element.width}
            height={element.height}
            fill={getPlaceholderColor()}
            stroke={isDragOver ? '#4f2683' : 'rgba(79, 38, 131, 0.3)'}
            strokeWidth={isDragOver ? 2 : 1}
            dash={[5, 5]}
          />

          {/* Placeholder text */}
          <Text
            x={0}
            y={0}
            width={element.width}
            height={element.height}
            text={getPlaceholderText()}
            fontSize={Math.min(12, element.width / 8)}
            fill="#666"
            align="center"
            verticalAlign="middle"
          />
        </>
      )}
    </Group>
  );
});

ImageElement.displayName = 'ImageElement';

export default ImageElement;
