import React from 'react';
import { useDispatch } from 'react-redux';
import {
  Box,
  Typography,

  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  QrCode as QRCodeIcon,
  CropFree as BarcodeIcon,
  Crop75 as RectangleIcon,
  Circle as CircleIcon,
  Star as StarIcon,
  Person as PersonIcon,
  Timeline as LineIcon,
  ChangeHistory as TriangleIcon,
  Hexagon as HexagonIcon,
} from '@mui/icons-material';

import { addElement } from '../../redux/idDesignerSlice';

const ToolPalette = () => {
  const dispatch = useDispatch();

  const handleAddElement = (elementType, properties = {}) => {
    const defaultProperties = {
      text: {
        type: 'text',
        text: 'Sample Text',
        fontSize: 16,
        fontFamily: 'Arial',
        color: '#4f2683', // Theme primary color
        textAlign: 'center',
        width: 120,
        height: 30,
      },
      staticImage: {
        type: 'image',
        src: null,
        width: 100,
        height: 100,
        placeholder: 'Static Image',
        imageType: 'static',
        acceptedTypes: 'image/jpeg,image/png,image/jpg,image/svg+xml',
        isDynamic: false,
      },
      dynamicImage: {
        type: 'image',
        src: null,
        width: 100,
        height: 100,
        placeholder: 'Dynamic Image',
        imageType: 'dynamic',
        acceptedTypes: 'image/jpeg,image/png,image/jpg,image/svg+xml',
        isDynamic: true,
        dynamicSrc: '{{image}}',
      },
      photo: {
        type: 'image',
        src: null,
        width: 80,
        height: 100,
        placeholder: 'Photo Placeholder',
        imageType: 'photo',
        acceptedTypes: 'image/jpeg,image/png,image/jpg',
        isDynamic: true,
        dynamicSrc: '{{photo}}',
      },
      qrcode: {
        type: 'qrcode',
        data: '{{id}}',
        size: 80,
        width: 80,
        height: 80,
        errorCorrectionLevel: 'M',
      },
      barcode: {
        type: 'barcode',
        data: '{{id}}',
        format: 'CODE128',
        width: 120,
        height: 40,
      },
      rectangle: {
        type: 'shape',
        shapeType: 'rectangle',
        width: 100,
        height: 60,
        fill: 'rgba(79, 38, 131, 0.1)', // Theme primary with transparency
        stroke: '#4f2683', // Theme primary color
        strokeWidth: 1,
      },
      circle: {
        type: 'shape',
        shapeType: 'circle',
        width: 80,
        height: 80,
        fill: 'rgba(79, 38, 131, 0.1)', // Theme primary with transparency
        stroke: '#4f2683', // Theme primary color
        strokeWidth: 1,
      },
      line: {
        type: 'shape',
        shapeType: 'line',
        width: 100,
        height: 2,
        fill: 'transparent',
        stroke: '#333333',
        strokeWidth: 2,
      },
      triangle: {
        type: 'shape',
        shapeType: 'triangle',
        width: 80,
        height: 80,
        fill: '#fff3e0',
        stroke: '#f57c00',
        strokeWidth: 1,
      },
      star: {
        type: 'shape',
        shapeType: 'star',
        width: 80,
        height: 80,
        fill: '#fff8e1',
        stroke: '#fbc02d',
        strokeWidth: 1,
        numPoints: 5,
      },
      polygon: {
        type: 'shape',
        shapeType: 'polygon',
        width: 80,
        height: 80,
        fill: '#e8f5e8',
        stroke: '#4caf50',
        strokeWidth: 1,
        sides: 6,
      },
    };

    const elementProps = {
      ...defaultProperties[elementType],
      ...properties,
    };

    dispatch(addElement(elementProps));
  };

  const ToolButton = ({ icon, label, onClick, color = 'primary', tooltip }) => (
    <Tooltip title={tooltip || label}>
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          cursor: 'pointer',
          textAlign: 'center',
          transition: 'all 0.2s',
          '&:hover': {
            elevation: 3,
            bgcolor: 'action.hover',
          },
        }}
        onClick={onClick}
      >
        <Box sx={{ color: `${color}.main`, mb: 0.5 }}>
          {icon}
        </Box>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {label}
        </Typography>
      </Paper>
    </Tooltip>
  );

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Design Tools
        </Typography>
      </Box>

      {/* Static Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Static Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <ToolButton
                icon={<TextIcon />}
                label="Static Text"
                onClick={() => handleAddElement('text', { text: 'Static Text', autoResize: false })}
              />
            </Grid>
            <Grid item xs={12}>
              <ToolButton
                icon={<ImageIcon />}
                label="Static Image"
                onClick={() => handleAddElement('staticImage')}
                tooltip="Upload your own image"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Dynamic Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Dynamic Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <ToolButton
                icon={<TextIcon />}
                label="Dynamic Text"
                onClick={() => handleAddElement('text', { text: '{{name}}', autoResize: true })}
                color="secondary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<ImageIcon />}
                label="Dynamic Image"
                onClick={() => handleAddElement('dynamicImage')}
                color="secondary"
                tooltip="Template-based image (no upload)"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<PersonIcon />}
                label="Photo"
                onClick={() => handleAddElement('photo')}
                color="primary"
                tooltip="Dynamic photo placeholder"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<QRCodeIcon />}
                label="QR Code"
                onClick={() => handleAddElement('qrcode')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<BarcodeIcon />}
                label="Barcode"
                onClick={() => handleAddElement('barcode')}
                color="primary"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>



      {/* Shape Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Shapes</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<RectangleIcon />}
                label="Rectangle"
                onClick={() => handleAddElement('rectangle')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<CircleIcon />}
                label="Circle"
                onClick={() => handleAddElement('circle')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<LineIcon />}
                label="Line"
                onClick={() => handleAddElement('line')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<TriangleIcon />}
                label="Triangle"
                onClick={() => handleAddElement('triangle')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<StarIcon />}
                label="Star"
                onClick={() => handleAddElement('star')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<HexagonIcon />}
                label="Polygon"
                onClick={() => handleAddElement('polygon')}
                color="error"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>


    </Box>
  );
};

export default ToolPalette;
